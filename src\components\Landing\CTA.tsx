// components/Landing/CTA.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Button from './Button';

const ctaVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } }
};

const CTA: React.FC = () => {
  return (
    <section className="bg-gradient-to-r from-[#0A66C2] to-[#0A66C2]/80 py-16">
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ctaVariants}
        className="container mx-auto px-4 text-center"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Join Today and Start Growing Your Career</h2>
        <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">Thousands of professionals are advancing their careers every day. Don't miss out on opportunities.</p>
        <Button href="#" variant="white" className="px-8 py-3">
          Get Started - It's Free
        </Button>
      </motion.div>
    </section>
  );
};

export default CTA;