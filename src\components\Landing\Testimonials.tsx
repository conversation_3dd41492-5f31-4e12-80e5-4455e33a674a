// components/Landing/Testimonials.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Card from './Card';

const testimonialsData = [
  {
    image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWwlMjB3b21hbnxlbnwwfHx8fDE3NTU1OTM4MjB8MA&ixlib=rb-4.1.0&q=80&w=1080',
    name: '<PERSON>',
    role: 'Senior Product Manager at TechCorp',
    quote: '"ProConnect helped me land my dream job at a top tech company. The networking features allowed me to connect with hiring managers directly!"'
  },
  {
    image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWwlMjBtYW58ZW58MHx8fHwxNzU1NTkzODIzfDA&ixlib=rb-4.1.0&q=80&w=1080',
    name: 'Michael Chen',
    role: 'Software Engineer at InnovateTech',
    quote: '"I expanded my professional network tenfold within just six months. The community groups helped me stay up-to-date with industry trends."'
  },
  {
    image: 'https://images.unsplash.com/photo-1580894732444-8ecded7900cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwyfHxwcm9mZXNzaW9uYWwlMjB3b21hbnxlbnwwfHx8fDE3NTU1OTM4MjB8MA&ixlib=rb-4.1.0&q=80&w=1080',
    name: 'Emily Rodriguez',
    role: 'Marketing Director at BrandGrowth',
    quote: '"The job recommendations were spot-on! I found opportunities I wouldn\'t have discovered elsewhere, leading to a 30% salary increase."'
  },
  {
    image: 'https://images.unsplash.com/photo-1568316674077-d72ee56de61c?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwzfHxwcm9mZXNzaW9uYWwlMjBtYW58ZW58MHx8fHwxNzU1NTkzODIzfDA&ixlib=rb-4.1.0&q=80&w=1080',
    name: 'David Williams',
    role: 'Finance Manager at GlobalFirm',
    quote: '"ProConnect\'s community features helped me connect with mentors in my field who provided invaluable career guidance and support."'
  }
];

const testimonialsVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.5 } }
};

const Testimonials: React.FC = () => {
  return (
    <section className="container mx-auto px-4 py-16 md:py-24">
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12"
      >
        Success Stories
      </motion.h2>
      <div className="flex overflow-x-auto pb-6 snap-x snap-mandatory space-x-6 hide-scrollbar">
        {testimonialsData.map((testimonial, index) => (
          <motion.div
            key={index}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={testimonialsVariants}
            className="min-w-[300px] md:min-w-[350px] snap-center"
          >
            <Card>
              <div className="flex items-center mb-4">
                <img src={testimonial.image} alt="User testimonial" className="w-14 h-14 rounded-full object-cover mr-4" />
                <div>
                  <h3 className="font-bold text-gray-900">{testimonial.name}</h3>
                  <p className="text-gray-600 text-sm">{testimonial.role}</p>
                </div>
              </div>
              <p className="text-gray-700">{testimonial.quote}</p>
            </Card>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default Testimonials;