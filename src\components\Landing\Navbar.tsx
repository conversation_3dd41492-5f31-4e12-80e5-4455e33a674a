// components/Landing/Navbar.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Button from './Button';

const navItems = [
  { label: 'Home', href: '#' },
  { label: 'About', href: '#' },
  { label: 'Jobs', href: '#' },
  { label: 'Network', href: '#' },
  { label: 'Community', href: '#' }
];

const navbarVariants = {
  hidden: { y: -50, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.5 } }
};

const Navbar: React.FC = () => {
  return (
    <motion.nav
      initial="hidden"
      animate="visible"
      variants={navbarVariants}
      className="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm transition-all duration-300 ease-in-out"
    >
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <a href="#" className="text-2xl font-bold text-[#0A66C2] flex items-center">
            <svg className="w-8 h-8 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" fill="#0A66C2" />
              <path d="M6 9H2V21H6V9Z" fill="#0A66C2" />
              <circle cx="4" cy="4" r="2" fill="#0A66C2" />
            </svg>
            ProConnect
          </a>
        </div>
        <div className="hidden md:flex items-center space-x-8">
          {navItems.map((item, index) => (
            <motion.a
              key={index}
              href={item.href}
              className="text-gray-700 hover:text-[#0A66C2] transition-colors duration-300"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {item.label}
            </motion.a>
          ))}
        </div>
        <div className="flex items-center space-x-4">
          <a href="#" className="hidden md:inline-block text-gray-700 hover:text-[#0A66C2] transition-colors duration-300">
            Sign In
          </a>
          <Button href="#" variant="primary">
            Join Now
          </Button>
          <button className="md:hidden rounded-full p-2 text-gray-700 hover:bg-gray-100 transition-colors duration-300">
            <span className="material-symbols-outlined">menu</span>
          </button>
        </div>
      </div>
    </motion.nav>
  );
};

export default Navbar;