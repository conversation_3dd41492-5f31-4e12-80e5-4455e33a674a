// components/Landing/Button.tsx (Universal reusable button component)
import React from 'react';
import { motion } from 'framer-motion';

interface ButtonProps {
  children: React.ReactNode;
  href?: string;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'white';
}

const buttonVariants = {
  hover: { scale: 1.05, boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)' },
  tap: { scale: 0.95 }
};

const Button: React.FC<ButtonProps> = ({ children, href, className = '', type = 'button', onClick, variant = 'primary' }) => {
  let baseClass = 'px-4 py-2 rounded-full font-medium transition-all duration-300 hover:shadow-lg';
  if (variant === 'primary') {
    baseClass += ' bg-[#0A66C2] hover:bg-[#084e96] text-white';
  } else if (variant === 'secondary') {
    baseClass += ' bg-white border border-[#0A66C2] text-[#0A66C2] hover:bg-gray-50';
  } else if (variant === 'white') {
    baseClass += ' bg-white text-[#0A66C2] hover:bg-gray-100';
  }

  const Component = href ? 'a' : 'button';

  return (
    <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
      <Component href={href} type={type} onClick={onClick} className={`${baseClass} ${className}`}>
        {children}
      </Component>
    </motion.div>
  );
};

export default Button;