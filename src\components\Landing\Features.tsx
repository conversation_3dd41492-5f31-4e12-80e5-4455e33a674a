// components/Landing/Features.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Card from './Card';
import IconCircle from './IconCircle';

const featuresData = [
  {
    icon: 'person',
    title: 'Build Your Professional Profile',
    description: 'Create a compelling profile that showcases your skills, experiences, and achievements.'
  },
  {
    icon: 'work',
    title: 'Explore Jobs',
    description: 'Discover thousands of job opportunities tailored to your skills and career aspirations.'
  },
  {
    icon: 'share',
    title: 'Expand Your Network',
    description: 'Connect with industry leaders, colleagues, and potential mentors to grow your network.'
  },
  {
    icon: 'notifications',
    title: 'Stay Updated',
    description: 'Get the latest industry news, trends, and updates from your professional network.'
  }
];

const featuresVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.2 } }
};

const Features: React.FC = () => {
  return (
    <section className="container mx-auto px-4 py-16 md:py-24">
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12"
      >
        Why Join ProConnect?
      </motion.h2>
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={featuresVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
      >
        {featuresData.map((feature, index) => (
          <motion.div key={index} variants={featuresVariants}>
            <Card>
              <IconCircle icon={feature.icon} />
              <h3 className="text-xl font-bold text-gray-900 mb-3 mt-6">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
};

export default Features;