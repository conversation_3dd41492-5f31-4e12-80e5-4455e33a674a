// components/Landing/IconCircle.tsx (Universal for icon circles in features, community, etc.)
import React from 'react';

interface IconCircleProps {
  icon: string;
  color?: string;
}

const IconCircle: React.FC<IconCircleProps> = ({ icon, color = '#0A66C2' }) => {
  return (
    <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center" style={{ color }}>
      <span className="material-symbols-outlined text-3xl">{icon}</span>
    </div>
  );
};

export default IconCircle;