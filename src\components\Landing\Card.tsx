// components/Landing/Card.tsx (Universal reusable card component for features, testimonials, jobs, etc.)
import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

const cardVariants = {
  hover: { scale: 1.05, y: -5, boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)' }
};

const Card: React.FC<CardProps> = ({ children, className = '' }) => {
  return (
    <motion.div
      variants={cardVariants}
      whileHover="hover"
      className={`bg-white p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 ${className}`}
    >
      {children}
    </motion.div>
  );
};

export default Card;