'use client'
// pages/index.tsx (or the main page file in Next.js)
import React from 'react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Landing/Navbar';
import Hero from '@/components/Landing/Hero';
import Signup from '@/components/Landing/Signup';
import Features from '@/components/Landing/Features';
import Stats from '@/components/Landing/Stats';
import Testimonials from '@/components/Landing/Testimonials';
import Jobs from '@/components/Landing/Jobs';
import Community from '@/components/Landing/Community';
import CTA from '@/components/Landing/CTA';
import Footer from '@/components/Landing/Footer';

const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.5
};

export default function Home() {
  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
      className="min-h-screen bg-white font-sans"
    >
      <Navbar />
      <Hero />
      <Signup />
      <Features />
      <Stats />
      <Testimonials />
      <Jobs />
      <Community />
      <CTA />
      <Footer />
    </motion.div>
  );
}