// components/Landing/Jobs.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Card from './Card';
import Button from './Button';

const jobsData = [
  {
    company: 'Google',
    location: 'Mountain View, CA (Remote)',
    title: 'Senior UX Designer',
    description: 'Create user-centered designs for Google\'s flagship products. 5+ years experience required.',
    tags: ['Full-time', 'Senior Level', 'Design'],
    logo: 'G'
  },
  {
    company: 'Microsoft',
    location: 'Redmond, WA (Hybrid)',
    title: 'Software Engineer',
    description: 'Develop and maintain cloud services for Microsoft Azure. Strong knowledge of C# and .NET required.',
    tags: ['Full-time', 'Mid Level', 'Engineering'],
    logo: 'M'
  },
  {
    company: 'Amazon',
    location: 'Seattle, WA (On-site)',
    title: 'Product Manager',
    description: 'Lead product development for Amazon\'s consumer products. MBA preferred with 3+ years experience.',
    tags: ['Full-time', 'Senior Level', 'Product'],
    logo: 'A'
  }
];

const jobsVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.2 } }
};

const Jobs: React.FC = () => {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-10">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900"
          >
            Trending Jobs
          </motion.h2>
          <Button href="#" className="text-[#0A66C2] hover:text-[#084e96] font-medium flex items-center">
            View All Jobs <span className="material-symbols-outlined ml-1">arrow_forward</span>
          </Button>
        </div>
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={jobsVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {jobsData.map((job, index) => (
            <motion.div key={index} variants={jobsVariants}>
              <Card>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span className="font-bold text-[#0A66C2]">{job.logo}</span>
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900">{job.company}</h3>
                    <p className="text-gray-600 text-sm">{job.location}</p>
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{job.title}</h3>
                <p className="text-gray-600 mb-4">{job.description}</p>
                <div className="flex flex-wrap gap-2">
                  {job.tags.map((tag, tagIndex) => (
                    <span key={tagIndex} className="px-3 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                      {tag}
                    </span>
                  ))}
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Jobs;