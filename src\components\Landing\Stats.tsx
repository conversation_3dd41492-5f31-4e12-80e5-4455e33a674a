// components/Landing/Stats.tsx
import React from 'react';
import { motion } from 'framer-motion';

const statsData = [
  { value: '10M+', label: 'Users Worldwide' },
  { value: '50K+', label: 'Companies' },
  { value: '200+', label: 'Countries' },
  { value: '5M+', label: 'Jobs Posted' }
];

const statsVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } }
};

const Stats: React.FC = () => {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
          {statsData.map((stat, index) => (
            <motion.div
              key={index}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={statsVariants}
              whileHover={{ scale: 1.05 }}
              className="p-6"
            >
              <p className="text-5xl font-bold text-[#0A66C2] mb-2">{stat.value}</p>
              <p className="text-gray-600 text-lg">{stat.label}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Stats;