// components/Landing/Community.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Button from './Button';
import IconCircle from './IconCircle';

const communityFeatures = [
  {
    icon: 'group',
    title: 'Industry Groups',
    description: 'Join specialized groups in your industry to discuss trends and challenges.'
  },
  {
    icon: 'event',
    title: 'Virtual Events',
    description: 'Participate in webinars, workshops, and networking events from anywhere.'
  },
  {
    icon: 'forum',
    title: 'Discussion Forums',
    description: 'Engage in meaningful conversations with professionals worldwide.'
  }
];

const communityVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const Community: React.FC = () => {
  return (
    <section className="container mx-auto px-4 py-16 md:py-24">
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={communityVariants}
        className="flex flex-col md:flex-row items-center"
      >
        <div className="w-full md:w-1/2 mb-10 md:mb-0 md:pr-10">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Join Our Professional Community</h2>
          <p className="text-xl text-gray-600 mb-8">Connect with like-minded professionals, join discussion groups, and participate in virtual events to expand your network.</p>
          <div className="space-y-4">
            {communityFeatures.map((feature, index) => (
              <div key={index} className="flex items-start">
                <IconCircle icon={feature.icon} />
                <div className="ml-4">
                  <h3 className="font-bold text-gray-900 mb-1">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
          <Button href="#" variant="primary" className="mt-8 px-6 py-3">
            Join a Group
          </Button>
        </div>
        <div className="w-full md:w-1/2">
          <motion.img
            src="https://images.unsplash.com/photo-1507679799987-c73779587ccf?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWx8ZW58MHx8fHwxNzU1NTQ4NDYzfDA&ixlib=rb-4.1.0&q=80&w=1080"
            alt="Professional community and networking"
            className="w-full rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
            whileHover={{ scale: 1.05 }}
          />
        </div>
      </motion.div>
    </section>
  );
};

export default Community;