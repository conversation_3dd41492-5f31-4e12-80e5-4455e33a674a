// components/Landing/Hero.tsx
import React from 'react';
import { motion } from 'framer-motion';
import Button from './Button';

const heroVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.2 } }
};

const childVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const Hero: React.FC = () => {
  return (
    <motion.section
      initial="hidden"
      animate="visible"
      variants={heroVariants}
      className="container mx-auto px-4 py-16 md:py-24 flex flex-col md:flex-row items-center"
    >
      <motion.div variants={childVariants} className="w-full md:w-1/2 mb-10 md:mb-0">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6">
          Connect with professionals, discover opportunities, and grow your career
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-lg">
          Join our global community of professionals to collaborate, learn, and advance your career path in a supportive environment.
        </p>
        <div className="flex flex-wrap gap-4">
          <Button href="#" variant="primary" className="px-8 py-3">
            Join Now
          </Button>
          <Button href="#" variant="secondary" className="px-8 py-3">
            Sign In
          </Button>
        </div>
      </motion.div>
      <motion.div variants={childVariants} className="w-full md:w-1/2 flex justify-center">
        <motion.img
          src="https://images.unsplash.com/photo-1550177977-ad69e8f3cae0?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MzkyNDZ8MHwxfHNlYXJjaHwxfHxuZXR3b3JraW5nfGVufDB8fHx8MTc1NTU5MzgxMXww&ixlib=rb-4.1.0&q=80&w=1080"
          alt="Professional networking illustration"
          className="w-full max-w-lg object-cover"
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.5 }}
        />
      </motion.div>
    </motion.section>
  );
};

export default Hero;