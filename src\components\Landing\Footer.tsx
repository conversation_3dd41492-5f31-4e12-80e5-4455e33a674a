// components/Landing/Footer.tsx
import React from 'react';
import { motion } from 'framer-motion';

const footerSections = [
  {
    title: 'Company',
    links: [
      { label: 'About', href: '#' },
      { label: 'Careers', href: '#' },
      { label: 'Contact', href: '#' },
      { label: 'Blog', href: '#' }
    ]
  },
  {
    title: 'Support',
    links: [
      { label: 'Help Center', href: '#' },
      { label: 'Privacy Policy', href: '#' },
      { label: 'Terms of Service', href: '#' },
      { label: 'Accessibility', href: '#' }
    ]
  },
  {
    title: 'Discover',
    links: [
      { label: 'Jobs', href: '#' },
      { label: 'Network', href: '#' },
      { label: 'Groups', href: '#' },
      { label: 'Events', href: '#' }
    ]
  }
];

const socialIcons = [
  { icon: 'fa-brands fa-linkedin', href: '#', hoverColor: '#0A66C2' },
  { icon: '', href: '#', hoverColor: '#1DA1F2' }  // Assuming Twitter/X icon, but empty in original
];

const footerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-100 pt-16 pb-8">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={footerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
        >
          {footerSections.map((section, index) => (
            <motion.div key={index} variants={footerVariants}>
              <h3 className="text-lg font-bold text-gray-900 mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a href={link.href} className="text-gray-600 hover:text-[#0A66C2] transition-colors duration-300">
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
          <motion.div variants={footerVariants}>
            <h3 className="text-lg font-bold text-gray-900 mb-4">Connect</h3>
            <div className="flex space-x-4">
              {socialIcons.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-700 transition-all duration-300"
                  whileHover={{ backgroundColor: social.hoverColor, color: '#fff' }}
                >
                  <i className={social.icon}></i>
                </motion.a>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;